/* Billing Reports Styles */

.billing-reports-container {
  padding: 20px;
  background-color: #f8f9fc;
  min-height: 100vh;
}

/* Statistics Cards */
.border-left-primary {
  border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
  border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
  border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

/* SID Autocomplete Dropdown */
.sid-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dee2e6;
  border-top: none;
  border-radius: 0 0 0.375rem 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.sid-suggestion-item {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.15s ease-in-out;
}

.sid-suggestion-item:hover {
  background-color: #f8f9fa;
}

.sid-suggestion-item:last-child {
  border-bottom: none;
}

/* Table Enhancements */
.table th {
  background-color: #f8f9fc;
  border-top: none;
  font-weight: 600;
  color: #5a5c69;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table td {
  vertical-align: middle;
  padding: 0.75rem;
  border-top: 1px solid #e3e6f0;
}

.table tbody tr:hover {
  background-color: #f8f9fc;
}

/* Status Badges */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
}

/* Action Buttons */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.2rem;
}

/* Card Enhancements */
.card {
  border: none;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.card-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
}

/* Modal Enhancements */
.modal-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
}

.modal-title {
  color: #5a5c69;
  font-weight: 600;
}

/* Search Form Enhancements */
.form-label {
  font-weight: 600;
  color: #5a5c69;
  margin-bottom: 0.5rem;
}

.form-control:focus {
  border-color: #4e73df;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Loading States */
.spinner-border {
  width: 1.5rem;
  height: 1.5rem;
}

/* Empty State */
.text-muted {
  color: #858796 !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .billing-reports-container {
    padding: 10px;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
  
  .btn-sm {
    padding: 0.125rem 0.25rem;
    font-size: 0.6875rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .modal-dialog {
    margin: 0.5rem;
  }
}

/* Print Styles */
@media print {
  .billing-reports-container {
    background-color: white !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #dee2e6 !important;
  }
  
  .btn, .modal, .alert {
    display: none !important;
  }
}

/* Accessibility Enhancements */
.btn:focus,
.form-control:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Animation for loading states */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeIn 0.3s ease-in-out;
}

/* Custom scrollbar for suggestions dropdown */
.sid-suggestions-dropdown::-webkit-scrollbar {
  width: 6px;
}

.sid-suggestions-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sid-suggestions-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sid-suggestions-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Test match rate indicators */
.match-rate-high {
  color: #1cc88a !important;
}

.match-rate-medium {
  color: #f6c23e !important;
}

.match-rate-low {
  color: #e74a3b !important;
}

/* Financial summary styling */
.financial-summary {
  background-color: #f8f9fc;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-top: 1rem;
}

.financial-summary .row {
  margin-bottom: 0.5rem;
}

.financial-summary .row:last-child {
  margin-bottom: 0;
  border-top: 1px solid #e3e6f0;
  padding-top: 0.5rem;
  font-weight: 600;
}

/* Test details table in modal */
.modal .table-sm th,
.modal .table-sm td {
  padding: 0.5rem;
  font-size: 0.875rem;
}

.modal .table-sm th {
  background-color: #f8f9fc;
  font-weight: 600;
  color: #5a5c69;
}

/* Status indicators */
.status-generated {
  background-color: #1cc88a;
}

.status-pending {
  background-color: #f6c23e;
}

.status-failed {
  background-color: #e74a3b;
}

.status-processing {
  background-color: #36b9cc;
}
