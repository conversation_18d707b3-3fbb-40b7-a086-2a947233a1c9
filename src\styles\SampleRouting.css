/* Sample Routing Styles */

.sample-routing-container {
  padding: 1rem;
}

/* Tab Styles */
.nav-tabs {
  border-bottom: 1px solid var(--border-color);
}

.nav-tabs .nav-link {
  margin-bottom: -1px;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  color: var(--dark-gray);
  font-weight: 600;
  padding: 0.75rem 1.25rem;
  transition: var(--transition);
}

.nav-tabs .nav-link:hover {
  border-color: var(--border-color) var(--border-color) transparent;
  color: var(--primary);
}

.nav-tabs .nav-link.active {
  color: var(--primary);
  background-color: var(--white);
  border-color: var(--border-color) var(--border-color) var(--white);
  border-top: 3px solid var(--primary);
  padding-top: calc(0.75rem - 2px);
}

/* Badge Styles */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.35em 0.65em;
  border-radius: 50rem;
}

/* Card Styles */
.border-left-warning {
  border-left: 0.25rem solid var(--warning) !important;
}

.border-left-info {
  border-left: 0.25rem solid var(--info) !important;
}

.border-left-success {
  border-left: 0.25rem solid var(--success) !important;
}

.border-left-danger {
  border-left: 0.25rem solid var(--danger) !important;
}

.card .text-xs {
  font-size: 0.7rem;
}

.card .text-uppercase {
  text-transform: uppercase !important;
}

.card .fa-2x {
  font-size: 2rem;
}

.card .text-gray-300 {
  color: #dddfeb !important;
}

.card .text-gray-800 {
  color: #5a5c69 !important;
}

/* Alert Styles */
.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.alert .btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.alert .btn-info:hover {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
}

/* Mobile Routing Cards */
.routing-mobile-card {
  margin-bottom: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color, #e0e0e0);
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.routing-mobile-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--primary, #d4006e);
}

.routing-mobile-card .mobile-card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid var(--border-color, #e0e0e0);
  padding: 1rem;
}

.routing-mobile-card .mobile-card-title {
  font-weight: 600;
  font-size: 1rem;
  color: var(--dark-gray, #2c2c2c);
  margin-bottom: 0.25rem;
}

.routing-mobile-card .mobile-card-subtitle {
  font-size: 0.875rem;
  color: var(--medium-gray, #5a5a5a);
}

.routing-mobile-card .mobile-card-body {
  padding: 1rem;
}

.routing-mobile-card .mobile-card-field {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.routing-mobile-card .mobile-card-field:last-child {
  border-bottom: none;
}

.routing-mobile-card .mobile-card-label {
  font-weight: 600;
  color: var(--medium-gray, #5a5a5a);
  font-size: 0.875rem;
  flex: 0 0 45%;
}

.routing-mobile-card .mobile-card-value {
  font-size: 0.875rem;
  color: var(--dark-gray, #2c2c2c);
  text-align: right;
  flex: 1;
  font-weight: 500;
}

.routing-mobile-card .mobile-card-actions {
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-top: 1px solid var(--border-color, #e0e0e0);
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.routing-mobile-card .mobile-action-btn {
  min-height: 44px;
  min-width: 80px;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.routing-mobile-card .mobile-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* Mobile Container Styles */
.mobile-routing-container .mobile-data-container {
  padding: 0.5rem;
}

.desktop-routing-container,
.mobile-routing-container {
  width: 100%;
  max-width: 100%;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
  .sample-routing-container h1 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .d-sm-flex {
    display: block !important;
  }

  .d-sm-flex .btn {
    width: 100%;
    margin-top: 1rem;
  }

  .nav-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .table th,
  .table td {
    font-size: 0.85rem;
    padding: 0.5rem;
  }

  .btn-sm {
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
  }

  /* Mobile card responsive adjustments */
  .routing-mobile-card .mobile-card-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .routing-mobile-card .mobile-action-btn {
    width: 100%;
    min-width: auto;
  }

  .routing-mobile-card .mobile-card-field {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .routing-mobile-card .mobile-card-label {
    flex: none;
  }

  .routing-mobile-card .mobile-card-value {
    text-align: left;
    flex: none;
    width: 100%;
  }
}
