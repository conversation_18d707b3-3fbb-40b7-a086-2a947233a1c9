/* Admin Dashboard Styles - Mobile First */

.admin-dashboard-container {
  padding: 0.75rem;
  max-width: 100vw;
  overflow-x: hidden;
}

/* Ensure proper box-sizing */
.admin-dashboard-container *,
.admin-dashboard-container *::before,
.admin-dashboard-container *::after {
  box-sizing: border-box;
}

/* Card Styles */
.border-left-primary {
  border-left: 0.25rem solid var(--primary) !important;
}

.border-left-success {
  border-left: 0.25rem solid var(--success) !important;
}

.border-left-info {
  border-left: 0.25rem solid var(--info) !important;
}

.border-left-warning {
  border-left: 0.25rem solid var(--warning) !important;
}

.card .text-xs {
  font-size: 0.7rem;
}

.card .text-uppercase {
  text-transform: uppercase !important;
}

.card .fa-2x {
  font-size: 2rem;
}

.card .text-gray-300 {
  color: #dddfeb !important;
}

.card .text-gray-800 {
  color: #5a5c69 !important;
}

/* Tab Styles */
.nav-tabs {
  border-bottom: 1px solid var(--border-color);
}

.nav-tabs .nav-link {
  margin-bottom: -1px;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  color: var(--dark-gray);
  font-weight: 600;
  padding: 0.75rem 1.25rem;
  transition: var(--transition);
}

.nav-tabs .nav-link:hover {
  border-color: var(--border-color) var(--border-color) transparent;
  color: var(--primary);
}

.nav-tabs .nav-link.active {
  color: var(--primary);
  background-color: var(--white);
  border-color: var(--border-color) var(--border-color) var(--white);
  border-top: 3px solid var(--primary);
  padding-top: calc(0.75rem - 2px);
}

/* Chart Container */
.chart-container {
  position: relative;
  height: 300px;
  margin: 0 auto;
}

/* Badge Styles */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.35em 0.65em;
  border-radius: 50rem;
}

/* Button Styles */
.btn-outline-primary {
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover {
  color: var(--white);
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-secondary {
  color: var(--secondary);
  border-color: var(--secondary);
}

.btn-outline-secondary:hover {
  color: var(--white);
  background-color: var(--secondary);
  border-color: var(--secondary);
}

.btn-outline-info {
  color: var(--info);
  border-color: var(--info);
}

.btn-outline-info:hover {
  color: var(--white);
  background-color: var(--info);
  border-color: var(--info);
}

.btn-outline-danger {
  color: var(--danger);
  border-color: var(--danger);
}

.btn-outline-danger:hover {
  color: var(--white);
  background-color: var(--danger);
  border-color: var(--danger);
}

.d-grid {
  display: grid;
  gap: 0.5rem;
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table th {
  background-color: var(--primary);
  color: var(--white);
  font-weight: 600;
}

.table td {
  vertical-align: middle;
}

/* Admin Statistics Cards - Mobile Optimized */
.admin-stat-card {
  transition: all 0.3s ease-in-out;
  border-radius: 0.75rem;
  overflow: hidden;
}

.admin-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.admin-stat-card .card-body {
  min-height: 80px;
  display: flex;
  align-items: center;
}

.admin-stat-card .text-xs {
  font-size: 0.7rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.admin-stat-card .h5 {
  font-size: 1.25rem;
  font-weight: 800;
  line-height: 1.2;
}

.admin-stat-card .fa-lg {
  font-size: 1.5rem;
  opacity: 0.7;
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 575.98px) {
  .admin-dashboard-container {
    padding: 0.5rem;
  }

  .admin-stat-card .card-body {
    padding: 0.75rem !important;
    min-height: 70px;
  }

  .admin-stat-card .text-xs {
    font-size: 0.65rem;
  }

  .admin-stat-card .h5 {
    font-size: 1.1rem;
  }

  .admin-stat-card .fa-lg {
    font-size: 1.25rem;
  }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .admin-dashboard-container {
    padding: 0.75rem;
  }

  .admin-stat-card .card-body {
    padding: 1rem !important;
  }
}

@media (max-width: 767.98px) {
  .nav-tabs {
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--primary) transparent;
  }

  .nav-tabs::-webkit-scrollbar {
    height: 4px;
  }

  .nav-tabs::-webkit-scrollbar-track {
    background: transparent;
  }

  .nav-tabs::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 2px;
  }

  .nav-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 80px;
  }

  .chart-container {
    height: 200px;
    padding: 0.5rem;
  }

  .table-responsive {
    border-radius: 0.5rem;
    margin-bottom: 1rem;
  }

  .table th,
  .table td {
    font-size: 0.8rem;
    padding: 0.5rem 0.25rem;
  }

  .btn-sm {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
    min-height: 32px;
    min-width: 32px;
  }

  /* Improve card spacing on mobile */
  .card {
    margin-bottom: 1rem;
  }

  .card-header {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .card-body {
    padding: 0.75rem;
  }
}

/* Desktop styles */
@media (min-width: 768px) {
  .admin-dashboard-container {
    padding: 1.5rem;
  }

  .admin-stat-card .card-body {
    padding: 1.25rem !important;
  }
}
