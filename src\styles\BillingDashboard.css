/* Billing Dashboard Styles */
.billing-dashboard {
  padding: 20px;
  background-color: #f8f9fc;
  min-height: 100vh;
}

/* Dashboard Cards */
.border-left-primary {
  border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
  border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
  border-left: 0.25rem solid #36b9cc !important;
}

.border-left-danger {
  border-left: 0.25rem solid #e74a3b !important;
}

/* Card Styling */
.card {
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
  border: 1px solid #e3e6f0;
  border-radius: 0.35rem;
}

.card-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
}

/* Text Styling */
.text-xs {
  font-size: 0.7rem;
}

.text-gray-800 {
  color: #5a5c69 !important;
}

.text-gray-300 {
  color: #dddfeb !important;
}

/* Stats Cards */
.stats-card {
  transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
  transform: translateY(-2px);
}

/* Quick Actions */
.quick-actions .btn {
  transition: all 0.2s ease-in-out;
}

.quick-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Table Styling */
.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #5a5c69;
  background-color: #f8f9fc;
}

/* Status Badges */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Button Groups */
.btn-group .btn {
  margin-right: 2px;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

/* Search Section */
.search-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 0.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
}

.search-section .form-control {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.search-section .btn {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Invoice Status Section */
.status-item {
  padding: 0.5rem 0;
  border-bottom: 1px solid #e3e6f0;
}

.status-item:last-child {
  border-bottom: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .billing-dashboard {
    padding: 10px;
  }
  
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }
  
  .btn-group {
    flex-direction: column;
  }
  
  .btn-group .btn {
    margin-right: 0;
    margin-bottom: 2px;
  }
  
  .table-responsive {
    font-size: 0.875rem;
  }
}

/* Loading States */
.loading-card {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-state .fa-3x {
  opacity: 0.3;
}

/* Action Buttons */
.action-btn {
  transition: all 0.2s ease-in-out;
  border-radius: 0.25rem;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Dashboard Header */
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  margin-bottom: 0.5rem;
}

.dashboard-header p {
  opacity: 0.9;
  margin-bottom: 0;
}

/* Card Animations */
.card {
  transition: all 0.3s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
}

/* Invoice Link Styling */
.invoice-link {
  color: #4e73df;
  text-decoration: none;
  font-weight: 600;
}

.invoice-link:hover {
  color: #2e59d9;
  text-decoration: underline;
}

/* Status Indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-indicator.pending {
  background-color: #f6c23e;
}

.status-indicator.paid {
  background-color: #1cc88a;
}

.status-indicator.partial {
  background-color: #36b9cc;
}

.status-indicator.cancelled {
  background-color: #e74a3b;
}

/* Quick Stats Animation */
.quick-stat {
  transition: all 0.3s ease-in-out;
}

.quick-stat:hover {
  background-color: rgba(78, 115, 223, 0.05);
  border-radius: 0.25rem;
  padding: 0.25rem;
}

/* Currency Formatting */
.currency {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

/* Print Styles */
@media print {
  .billing-dashboard {
    background-color: white !important;
  }
  
  .btn, .card-header {
    display: none !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}
