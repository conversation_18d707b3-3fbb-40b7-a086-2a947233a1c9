import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Card, Button, Badge, Row, Col } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFileInvoiceDollar, faArrowLeft, faPrint, faMoneyBillWave,
  faUser, faCalendarAlt, faRupeeSign, faCheckCircle, faShare
} from '@fortawesome/free-solid-svg-icons';
import { billingAPI } from '../../services/api';
import { InfoModal, SuccessModal, ErrorModal } from '../../components/common';
import WhatsAppSend from '../../components/common/WhatsAppSend';
import ResponsiveInvoiceItemsTable from '../../components/billing/ResponsiveInvoiceItemsTable';
import '../../styles/BillingView.css';

const BillingView = () => {
  const { id } = useParams();
  const [billing, setBilling] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage] = useState('');

  // Fetch billing data
  useEffect(() => {
    const fetchBilling = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await billingAPI.getBillingById(id);
        setBilling(response.data);
      } catch (err) {
        console.error('Error fetching billing:', err);
        setError('Failed to load billing details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchBilling();
  }, [id]);

  // Handle share invoice
  const handleShare = () => {
    // Implementation for sharing invoice (e.g., via email or WhatsApp)
    setShowShareModal(false);
    setShowSuccessModal(true);
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case 'Pending':
        return 'warning';
      case 'Paid':
        return 'success';
      case 'Partial':
        return 'info';
      case 'Cancelled':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="text-center my-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading billing details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        {error}
      </div>
    );
  }

  if (!billing) {
    return (
      <div className="alert alert-warning" role="alert">
        Billing record not found.
      </div>
    );
  }

  return (
    <div className="billing-view-container">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="h3 mb-0 text-gray-800">
          <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
          Invoice Details
        </h1>
        <div>
          <Link to="/billing" className="btn btn-secondary me-2">
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back to List
          </Link>
          <Button variant="primary" className="me-2" onClick={() => window.print()}>
            <FontAwesomeIcon icon={faPrint} className="me-2" />
            Print
          </Button>
          {(billing.status === 'Pending' || billing.status === 'Partial') && (
            <Link to={`/billing/${id}/collect`} className="btn btn-success me-2">
              <FontAwesomeIcon icon={faMoneyBillWave} className="me-2" />
              Collect Payment
            </Link>
          )}
          <Button variant="info" onClick={() => setShowShareModal(true)}>
            <FontAwesomeIcon icon={faShare} className="me-2" />
            Share
          </Button>
        </div>
      </div>

      <Row>
        <Col lg={8}>
          <Card className="shadow mb-4">
            <Card.Header className="py-3">
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <h6 className="m-0 font-weight-bold text-primary">
                    Invoice #{billing.invoice_number}
                  </h6>
                  {billing.sid_number && (
                    <small className="text-muted">
                      SID: {billing.sid_number}
                    </small>
                  )}
                </div>
                <div className="text-end">
                  <Badge
                    bg={getStatusBadgeVariant(billing.status)}
                    className="mb-1"
                  >
                    {billing.status}
                  </Badge>
                  {billing.clinic && (
                    <div>
                      <small className="text-muted d-block">
                        {billing.clinic.name}
                      </small>
                    </div>
                  )}
                </div>
              </div>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <div className="billing-detail-item">
                    <FontAwesomeIcon icon={faCalendarAlt} className="me-2 text-primary" />
                    <strong>Invoice Date:</strong>
                    <span>{new Date(billing.invoice_date).toLocaleDateString()}</span>
                  </div>
                  {billing.due_date && (
                    <div className="billing-detail-item">
                      <FontAwesomeIcon icon={faCalendarAlt} className="me-2 text-warning" />
                      <strong>Due Date:</strong>
                      <span>{new Date(billing.due_date).toLocaleDateString()}</span>
                    </div>
                  )}
                  {billing.sid_number && (
                    <div className="billing-detail-item">
                      <strong>SID Number:</strong>
                      <span className="badge bg-info text-dark">{billing.sid_number}</span>
                    </div>
                  )}
                  {billing.invoice_info?.service_period && (
                    <div className="billing-detail-item">
                      <strong>Service Date:</strong>
                      <span>{new Date(billing.invoice_info.service_period).toLocaleDateString()}</span>
                    </div>
                  )}
                  {billing.invoice_info?.payment_terms && (
                    <div className="billing-detail-item">
                      <strong>Payment Terms:</strong>
                      <span>{billing.invoice_info.payment_terms}</span>
                    </div>
                  )}
                  <div className="billing-detail-item">
                    <FontAwesomeIcon icon={faRupeeSign} className="me-2 text-primary" />
                    <strong>Total Amount:</strong>
                    <span>{formatCurrency(billing.total_amount)}</span>
                  </div>
                  <div className="billing-detail-item">
                    <FontAwesomeIcon icon={faRupeeSign} className="me-2 text-primary" />
                    <strong>Paid Amount:</strong>
                    <span>{formatCurrency(billing.paid_amount)}</span>
                  </div>
                  <div className="billing-detail-item">
                    <FontAwesomeIcon icon={faRupeeSign} className="me-2 text-primary" />
                    <strong>Balance:</strong>
                    <span>{formatCurrency(billing.total_amount - billing.paid_amount)}</span>
                  </div>
                </Col>
                <Col md={6}>
                  {billing.patient && (
                    <>
                      <div className="billing-detail-item">
                        <FontAwesomeIcon icon={faUser} className="me-2 text-primary" />
                        <strong>Patient:</strong>
                        <span>
                          <Link to={`/patients/${billing.patient.id}`}>
                            {billing.patient.full_name || `${billing.patient.first_name} ${billing.patient.last_name}`}
                          </Link>
                        </span>
                      </div>
                      <div className="billing-detail-item">
                        <strong>Patient ID:</strong>
                        <span>{billing.patient.patient_id}</span>
                      </div>
                      <div className="billing-detail-item">
                        <strong>Date of Birth:</strong>
                        <span>
                          {billing.patient.date_of_birth ?
                            new Date(billing.patient.date_of_birth).toLocaleDateString() : 'N/A'}
                          {billing.patient.age && ` (Age: ${billing.patient.age})`}
                        </span>
                      </div>
                      <div className="billing-detail-item">
                        <strong>Gender:</strong>
                        <span>{billing.patient.gender || 'N/A'}</span>
                      </div>
                      <div className="billing-detail-item">
                        <strong>Contact:</strong>
                        <span>{billing.patient.phone}</span>
                      </div>
                      <div className="billing-detail-item">
                        <strong>Email:</strong>
                        <span>{billing.patient.email || 'N/A'}</span>
                      </div>
                      {billing.patient.full_address && (
                        <div className="billing-detail-item">
                          <strong>Address:</strong>
                          <span>{billing.patient.full_address}</span>
                        </div>
                      )}
                      {billing.patient.blood_group && (
                        <div className="billing-detail-item">
                          <strong>Blood Group:</strong>
                          <span>{billing.patient.blood_group}</span>
                        </div>
                      )}
                    </>
                  )}
                </Col>
              </Row>

              <hr />

              <ResponsiveInvoiceItemsTable
                items={billing.items || []}
                subtotal={billing.subtotal || 0}
                discount={billing.discount || 0}
                tax={billing.tax || 0}
                taxRate={billing.tax_rate || 0}
                totalAmount={billing.total_amount || 0}
                title="Invoice Items"
              />
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          {/* Clinic Information Card */}
          {billing.clinic && (
            <Card className="shadow mb-4">
              <Card.Header className="py-3">
                <h6 className="m-0 font-weight-bold text-primary">Clinic Information</h6>
              </Card.Header>
              <Card.Body>
                <div className="clinic-info">
                  <h6 className="text-primary">{billing.clinic.name}</h6>
                  {billing.clinic.address && (
                    <div className="mb-2">
                      <small className="text-muted">Address:</small>
                      <div>{billing.clinic.address}</div>
                    </div>
                  )}
                  {billing.clinic.phone && (
                    <div className="mb-2">
                      <small className="text-muted">Phone:</small>
                      <div>{billing.clinic.phone}</div>
                    </div>
                  )}
                  {billing.clinic.email && (
                    <div className="mb-2">
                      <small className="text-muted">Email:</small>
                      <div>{billing.clinic.email}</div>
                    </div>
                  )}
                  {billing.clinic.site_code && (
                    <div className="mb-2">
                      <small className="text-muted">Site Code:</small>
                      <span className="badge bg-secondary ms-2">{billing.clinic.site_code}</span>
                    </div>
                  )}
                </div>
              </Card.Body>
            </Card>
          )}

          {/* Payment & Tax Information Card */}
          <Card className="shadow mb-4">
            <Card.Header className="py-3">
              <h6 className="m-0 font-weight-bold text-primary">Payment & Tax Details</h6>
            </Card.Header>
            <Card.Body>
              <div className="payment-tax-info">
                {billing.invoice_info?.payment_terms && (
                  <div className="mb-2">
                    <small className="text-muted">Payment Terms:</small>
                    <div>{billing.invoice_info.payment_terms}</div>
                  </div>
                )}

                {billing.payment_method && (
                  <div className="mb-2">
                    <small className="text-muted">Payment Method:</small>
                    <span className="badge bg-info ms-2">{billing.payment_method}</span>
                  </div>
                )}

                {(billing.gst_rate || billing.tax_rate || billing.invoice_info?.tax_rate) && (
                  <div className="mb-2">
                    <small className="text-muted">GST Rate:</small>
                    <div>{billing.gst_rate || billing.tax_rate || billing.invoice_info?.tax_rate}%</div>
                  </div>
                )}

                {billing.gst_amount && (
                  <div className="mb-2">
                    <small className="text-muted">GST Amount:</small>
                    <div>{formatCurrency(billing.gst_amount)}</div>
                  </div>
                )}

                {billing.discount_percent && (
                  <div className="mb-2">
                    <small className="text-muted">Discount Applied:</small>
                    <div>{billing.discount_percent}%</div>
                  </div>
                )}
              </div>
            </Card.Body>
          </Card>

          <Card className="shadow mb-4">
            <Card.Header className="py-3">
              <h6 className="m-0 font-weight-bold text-primary">Payment History</h6>
            </Card.Header>
            <Card.Body>
              {billing.payments && billing.payments.length > 0 ? (
                <div className="payment-history">
                  {billing.payments.map((payment, index) => (
                    <div key={index} className="payment-item">
                      <div className="payment-date">
                        {new Date(payment.payment_date).toLocaleDateString()}
                      </div>
                      <div className="payment-details">
                        <div className="payment-amount">
                          {formatCurrency(payment.amount)}
                        </div>
                        <div className="payment-method">
                          {payment.payment_method}
                          {payment.status === 'Completed' && (
                            <FontAwesomeIcon icon={faCheckCircle} className="ms-2 text-success" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted">No payment records found.</p>
              )}

              {(billing.status === 'Pending' || billing.status === 'Partial') && (
                <div className="mt-3">
                  <Link to={`/billing/${id}/collect`} className="btn btn-success btn-block w-100">
                    <FontAwesomeIcon icon={faMoneyBillWave} className="me-2" />
                    Collect Payment
                  </Link>
                </div>
              )}
            </Card.Body>
          </Card>

          {/* WhatsApp Send Component */}
          <Card className="shadow mb-4">
            <Card.Header className="py-3">
              <h6 className="m-0 font-weight-bold">WhatsApp</h6>
            </Card.Header>
            <Card.Body>
              <WhatsAppSend
                type="invoice"
                patientName={billing.patient ? `${billing.patient.first_name} ${billing.patient.last_name}` : ''}
                billingId={billing.id}
                defaultPhone={billing.patient?.phone || ''}
                defaultMessage={`Your invoice is ready. Invoice #${billing.invoice_number}. Total amount: ${formatCurrency(billing.total_amount)}. Thank you for choosing AVINI LABS.`}
                onSuccess={(message) => {
                  // Show success notification
                  console.log('WhatsApp invoice sent:', message);
                }}
                onError={(error) => {
                  // Show error notification
                  console.error('WhatsApp error:', error);
                }}
              />
            </Card.Body>
          </Card>

          {/* Notes & Instructions Card */}
          {(billing.notes || billing.invoice_info?.notes) && (
            <Card className="shadow mb-4">
              <Card.Header className="py-3">
                <h6 className="m-0 font-weight-bold text-primary">Notes & Instructions</h6>
              </Card.Header>
              <Card.Body>
                <div className="notes-section">
                  {billing.notes && (
                    <div className="mb-2">
                      <small className="text-muted">Billing Notes:</small>
                      <div>{billing.notes}</div>
                    </div>
                  )}
                  {billing.invoice_info?.notes && billing.invoice_info.notes !== billing.notes && (
                    <div className="mb-2">
                      <small className="text-muted">Additional Notes:</small>
                      <div>{billing.invoice_info.notes}</div>
                    </div>
                  )}
                </div>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>

      {/* Share Modal */}
      <InfoModal
        show={showShareModal}
        onHide={() => setShowShareModal(false)}
        title="Share Invoice"
        message={
          <div>
            <p>Share invoice #{billing.invoice_number} with the patient:</p>
            <div className="d-grid gap-2">
              <Button variant="success" onClick={handleShare}>
                <i className="fab fa-whatsapp me-2"></i>
                Share via WhatsApp
              </Button>
              <Button variant="primary" onClick={handleShare}>
                <i className="fas fa-envelope me-2"></i>
                Share via Email
              </Button>
            </div>
          </div>
        }
      />

      {/* Success Modal */}
      <SuccessModal
        show={showSuccessModal}
        onHide={() => setShowSuccessModal(false)}
        title="Success"
        message="Invoice has been shared successfully!"
      />

      {/* Error Modal */}
      <ErrorModal
        show={showErrorModal}
        onHide={() => setShowErrorModal(false)}
        title="Error"
        message={errorMessage}
      />
    </div>
  );
};

export default BillingView;
